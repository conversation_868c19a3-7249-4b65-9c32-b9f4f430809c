{"name": "完整浏览器自动化工作流", "nodes": [{"parameters": {"options": {}}, "id": "f0b4e5d6-7c8a-9b1c-2d3e-4f5a6b7c8d9e", "name": "手动触发", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "user_input", "value": "请打开百度搜索辽宁天气预报"}]}, "options": {}}, "id": "a1b2c3d4-5e6f-7g8h-9i0j-1k2l3m4n5o6p", "name": "设置用户输入", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [460, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:11434/api/generate", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonParameters": {"parameters": [{"name": "model", "value": "qwen2.5:7b"}, {"name": "prompt", "value": "用户指令：{{ $json.user_input }}\n\n请分析这个指令，如果是搜索请求，请返回搜索URL。如果是打开网站请求，请返回网站URL。\n\n请只返回URL，不要其他内容。例如：https://www.baidu.com/s?wd=天气预报"}, {"name": "stream", "value": false}]}, "options": {}}, "id": "b2c3d4e5-6f7g-8h9i-0j1k-2l3m4n5o6p7q", "name": "AI分析指令", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// 处理 Ollama 的响应\nconst response = $input.all()[0].json.response;\n\n// 提取URL\nlet url = \"https://www.baidu.com/s?wd=辽宁天气预报\"; // 默认URL\n\nif (response && typeof response === 'string') {\n  // 查找URL模式\n  const urlMatch = response.match(/https?:\\/\\/[^\\s]+/);\n  if (urlMatch) {\n    url = urlMatch[0];\n  }\n}\n\n// 返回处理后的URL\nreturn {\n  json: {\n    url: url,\n    original_response: response\n  }\n};"}, "id": "c3d4e5f6-7g8h-9i0j-1k2l-3m4n5o6p7q8r", "name": "处理AI响应", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"method": "POST", "url": "http://127.0.0.1:8000/chrome_navigate", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonParameters": {"parameters": [{"name": "url", "value": "={{ $json.url }}"}]}, "options": {}}, "id": "d4e5f6g7-8h9i-0j1k-2l3m-4n5o6p7q8r9s", "name": "执行浏览器操作", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"values": {"boolean": [{"name": "success", "value": true}], "string": [{"name": "message", "value": "浏览器操作执行完成"}, {"name": "executed_url", "value": "={{ $('处理AI响应').item.json.url }}"}, {"name": "ai_response", "value": "={{ $('处理AI响应').item.json.original_response }}"}, {"name": "browser_result", "value": "={{ $json.data.content[0].text }}"}]}, "options": {}}, "id": "e5f6g7h8-9i0j-1k2l-3m4n-5o6p7q8r9s0t", "name": "返回执行结果", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1340, 300]}], "connections": {"手动触发": {"main": [[{"node": "设置用户输入", "type": "main", "index": 0}]]}, "设置用户输入": {"main": [[{"node": "AI分析指令", "type": "main", "index": 0}]]}, "AI分析指令": {"main": [[{"node": "处理AI响应", "type": "main", "index": 0}]]}, "处理AI响应": {"main": [[{"node": "执行浏览器操作", "type": "main", "index": 0}]]}, "执行浏览器操作": {"main": [[{"node": "返回执行结果", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "complete-browser-automation", "tags": ["浏览器自动化", "AI", "Ollama", "mcpo"]}