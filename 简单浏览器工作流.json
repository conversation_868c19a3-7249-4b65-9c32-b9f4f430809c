{"name": "简单浏览器工作流", "nodes": [{"parameters": {"options": {}}, "id": "trigger-simple", "name": "开始", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"values": {"string": [{"name": "url", "value": "https://www.baidu.com/s?wd=辽宁天气预报"}, {"name": "action", "value": "搜索辽宁天气预报"}]}, "options": {}}, "id": "set-simple", "name": "设置搜索URL", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [520, 300]}, {"parameters": {"requestMethod": "POST", "url": "http://127.0.0.1:8000/chrome_navigate", "jsonParameters": {"parameters": [{"name": "url", "value": "={{ $json.url }}"}]}, "options": {"headers": {"Content-Type": "application/json"}}}, "id": "http-simple", "name": "打开浏览器", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [740, 300]}, {"parameters": {"values": {"boolean": [{"name": "success", "value": true}], "string": [{"name": "message", "value": "成功打开百度搜索页面"}, {"name": "url", "value": "={{ $('设置搜索URL').item.json.url }}"}, {"name": "browser_response", "value": "={{ $json.data.content[0].text }}"}]}, "options": {}}, "id": "result-simple", "name": "显示结果", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [960, 300]}], "connections": {"开始": {"main": [[{"node": "设置搜索URL", "type": "main", "index": 0}]]}, "设置搜索URL": {"main": [[{"node": "打开浏览器", "type": "main", "index": 0}]]}, "打开浏览器": {"main": [[{"node": "显示结果", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "simple-browser-automation", "tags": ["简单", "浏览器", "mcpo"]}