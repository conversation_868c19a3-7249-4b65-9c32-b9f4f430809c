# Open WebUI 嵌入模型下载与配置教程

## 📖 概述

嵌入模型是 Open WebUI 实现 RAG（检索增强生成）功能的核心组件，用于将文档转换为向量进行语义搜索。本教程将指导您如何下载和配置嵌入模型。

## 🎯 默认配置

Open WebUI 默认使用的嵌入模型：
- **模型名称**: `sentence-transformers/all-MiniLM-L6-v2`
- **向量维度**: 384
- **模型大小**: 约 90MB
- **语言支持**: 多语言（包括中文）

## 🔍 检查模型状态

### 1. 查看配置信息
在 Open WebUI 启动日志中查找：
```
INFO [open_webui.env] VECTOR_DB: chroma
INFO [open_webui.env] Embedding model set: sentence-transformers/all-MiniLM-L6-v2
```

### 2. 测试模型是否工作
- 上传一个文档到知识库
- 观察日志中是否出现批处理进度条：
```
Batches: 100%|████████████████████| 1/1 [00:00<00:00, 6.47it/s]
```

## 🚀 自动下载（推荐）

### 方法1: 通过使用触发下载
1. 在 Open WebUI 中创建知识库
2. 上传任意文档（txt、pdf、docx等）
3. 系统会自动下载并缓存模型

### 方法2: 使用下载脚本
运行提供的下载脚本：
```bash
python download_embedding_model.py
```

## 🇨🇳 国内用户加速下载

### 设置镜像源
```bash
# Windows CMD
set HF_ENDPOINT=https://hf-mirror.com

# Windows PowerShell  
$env:HF_ENDPOINT="https://hf-mirror.com"

# Linux/Mac
export HF_ENDPOINT=https://hf-mirror.com
```

### 可用的国内镜像源
| 镜像源 | 地址 | 推荐度 |
|--------|------|--------|
| HF-Mirror | https://hf-mirror.com | ⭐⭐⭐⭐⭐ |
| 智谱AI | https://hub.zhipuai.cn | ⭐⭐⭐⭐ |
| 魔搭社区 | ModelScope | ⭐⭐⭐⭐⭐ |

## 🔧 手动下载方法

### 方法1: Python 脚本下载
```python
import os
# 设置国内镜像（可选）
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

from sentence_transformers import SentenceTransformer
model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
print(f"模型缓存位置: {model.cache_folder}")
```

### 方法2: 使用魔搭社区
```bash
# 安装 modelscope
pip install modelscope

# 下载模型
python -c "
from modelscope import snapshot_download
snapshot_download('AI-ModelScope/all-MiniLM-L6-v2', cache_dir='./models')
"
```

## 📁 模型存储位置

### Windows 系统
```
C:\Users\<USER>\.cache\huggingface\hub\models--sentence-transformers--all-MiniLM-L6-v2\
```

### Linux/Mac 系统
```
~/.cache/huggingface/hub/models--sentence-transformers--all-MiniLM-L6-v2/
```

### 自定义缓存位置
```bash
# 设置自定义缓存目录
set HUGGINGFACE_HUB_CACHE=D:\models\huggingface
set TRANSFORMERS_CACHE=D:\models\transformers
```

## 🛠️ 常见问题解决

### 问题1: 下载速度慢
**解决方案**: 使用国内镜像源
```bash
set HF_ENDPOINT=https://hf-mirror.com
```

### 问题2: 网络连接失败
**解决方案**: 
1. 检查网络连接
2. 尝试不同的镜像源
3. 使用魔搭社区下载

### 问题3: 磁盘空间不足
**解决方案**: 
1. 清理其他缓存文件
2. 设置自定义缓存目录到其他磁盘

### 问题4: 模型加载失败
**解决方案**:
1. 删除缓存文件重新下载
2. 检查文件完整性
3. 重启 Open WebUI

## 🔄 更换其他嵌入模型

### 支持的模型类型
- sentence-transformers 系列
- OpenAI embeddings
- 本地 ONNX 模型

### 配置方法
在 Open WebUI 设置中修改嵌入模型配置：
```
设置 → 管理员设置 → 文档 → 嵌入模型
```

## ✅ 验证安装成功

### 1. 检查日志输出
```
✅ 模型下载成功！
📁 模型缓存位置: C:\Users\<USER>\.cache\torch\sentence_transformers\...
🧪 模型测试成功！
📊 嵌入向量维度: (1, 384)
```

### 2. 功能测试
1. 上传文档到知识库
2. 询问文档相关问题
3. 检查是否能正确检索和回答

## 📊 性能优化建议

### 1. 硬件要求
- **内存**: 至少 4GB 可用内存
- **存储**: 至少 1GB 可用空间
- **CPU**: 支持 AVX 指令集（推荐）

### 2. 优化设置
- 使用 SSD 存储模型文件
- 确保充足的系统内存
- 关闭不必要的后台程序

## 🎯 总结

1. **自动下载**: 通过上传文档触发（最简单）
2. **手动下载**: 使用提供的脚本（更可控）
3. **国内加速**: 设置镜像源（提升速度）
4. **验证成功**: 检查日志和功能测试

按照本教程操作，您就能成功配置 Open WebUI 的嵌入模型，享受强大的 RAG 功能！
