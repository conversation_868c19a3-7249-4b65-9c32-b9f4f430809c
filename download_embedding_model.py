#!/usr/bin/env python3
"""
手动下载 sentence-transformers 嵌入模型
"""

import os
from sentence_transformers import SentenceTransformer

def download_embedding_model():
    """下载嵌入模型到本地缓存"""
    
    model_name = 'sentence-transformers/all-MiniLM-L6-v2'
    
    print(f"开始下载模型: {model_name}")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 下载并加载模型（会自动缓存到本地）
        model = SentenceTransformer(model_name)
        
        print(f"✅ 模型下载成功！")
        print(f"📁 模型缓存位置: {model.cache_folder}")
        
        # 测试模型是否正常工作
        test_text = "这是一个测试文本"
        embeddings = model.encode([test_text])
        
        print(f"🧪 模型测试成功！")
        print(f"📊 嵌入向量维度: {embeddings.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {str(e)}")
        return False

if __name__ == "__main__":
    download_embedding_model()
