#!/usr/bin/env python3
"""
手动下载 sentence-transformers 嵌入模型 - 支持国内镜像源
"""

import os
from sentence_transformers import SentenceTransformer

def download_embedding_model_china():
    """使用国内镜像下载嵌入模型"""

    # 设置国内镜像源
    print("🇨🇳 使用国内镜像源下载...")
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

    model_name = 'sentence-transformers/all-MiniLM-L6-v2'

    print(f"开始下载模型: {model_name}")
    print("使用 HF-Mirror 镜像源，速度更快...")

    try:
        # 下载并加载模型（会自动缓存到本地）
        model = SentenceTransformer(model_name)

        print(f"✅ 模型下载成功！")
        print(f"📁 模型缓存位置: {model.cache_folder}")

        # 测试模型是否正常工作
        test_text = "这是一个测试文本"
        embeddings = model.encode([test_text])

        print(f"🧪 模型测试成功！")
        print(f"📊 嵌入向量维度: {embeddings.shape}")

        return True

    except Exception as e:
        print(f"❌ 下载失败: {str(e)}")
        print("💡 尝试其他镜像源...")
        return download_embedding_model_backup()

def download_embedding_model_backup():
    """备用下载方法 - 智谱AI镜像"""

    print("🔄 尝试智谱AI镜像源...")
    os.environ['HF_ENDPOINT'] = 'https://hub.zhipuai.cn'

    model_name = 'sentence-transformers/all-MiniLM-L6-v2'

    try:
        model = SentenceTransformer(model_name)
        print(f"✅ 使用备用镜像下载成功！")
        return True
    except Exception as e:
        print(f"❌ 备用镜像也失败: {str(e)}")
        return False

def download_embedding_model_original():
    """原始方法 - 直接从 Hugging Face 下载"""

    # 清除镜像设置
    if 'HF_ENDPOINT' in os.environ:
        del os.environ['HF_ENDPOINT']

    model_name = 'sentence-transformers/all-MiniLM-L6-v2'

    print(f"🌍 使用原始 Hugging Face 源下载...")
    print("这可能需要较长时间，请耐心等待...")

    try:
        model = SentenceTransformer(model_name)
        print(f"✅ 原始源下载成功！")
        return True
    except Exception as e:
        print(f"❌ 原始源下载失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始下载嵌入模型...")
    print("📋 尝试顺序: 国内镜像 → 备用镜像 → 原始源")
    print("-" * 50)

    # 按优先级尝试不同的下载方法
    if not download_embedding_model_china():
        print("\n🔄 尝试原始 Hugging Face 源...")
        download_embedding_model_original()
