{"name": "浏览器自动化 - Ollama + mcpo", "nodes": [{"parameters": {}, "id": "f6b4c5d0-8e2a-4b1c-9d3e-1a2b3c4d5e6f", "name": "手动触发", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://localhost:11434/api/generate", "options": {"headers": {"Content-Type": "application/json"}, "body": {"model": "qwen2.5:7b", "prompt": "用户指令：{{ $json.user_input || '请打开百度搜索辽宁天气预报' }}\n\n请分析这个指令并返回浏览器操作。如果是搜索请求，直接返回搜索URL。", "stream": false}}}, "id": "a1b2c3d4-5e6f-7g8h-9i0j-k1l2m3n4o5p6", "name": "Ollama AI 分析", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"values": {"string": [{"name": "action", "value": "navigate"}, {"name": "url", "value": "https://www.baidu.com/s?wd=辽宁天气预报"}]}, "options": {}}, "id": "b2c3d4e5-6f7g-8h9i-0j1k-l2m3n4o5p6q7", "name": "设置浏览器操作", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [680, 300]}, {"parameters": {"url": "http://127.0.0.1:8000/chrome_navigate", "options": {"headers": {"Content-Type": "application/json"}, "body": {"url": "={{ $json.url }}"}}}, "id": "c3d4e5f6-7g8h-9i0j-1k2l-m3n4o5p6q7r8", "name": "执行浏览器操作", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300]}, {"parameters": {"values": {"boolean": [{"name": "success", "value": true}], "string": [{"name": "message", "value": "浏览器操作执行完成"}, {"name": "executed_url", "value": "={{ $('设置浏览器操作').item.json.url }}"}]}, "options": {}}, "id": "d4e5f6g7-8h9i-0j1k-2l3m-n4o5p6q7r8s9", "name": "返回结果", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1120, 300]}], "connections": {"手动触发": {"main": [[{"node": "Ollama AI 分析", "type": "main", "index": 0}]]}, "Ollama AI 分析": {"main": [[{"node": "设置浏览器操作", "type": "main", "index": 0}]]}, "设置浏览器操作": {"main": [[{"node": "执行浏览器操作", "type": "main", "index": 0}]]}, "执行浏览器操作": {"main": [[{"node": "返回结果", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "1", "tags": []}