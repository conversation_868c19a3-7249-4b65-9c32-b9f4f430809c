{"name": "无AI浏览器工作流", "nodes": [{"parameters": {}, "name": "开始", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [300, 300], "id": "start-1"}, {"parameters": {"values": {"string": [{"name": "url", "value": "https://www.baidu.com/s?wd=辽宁天气预报"}, {"name": "action", "value": "搜索辽宁天气预报"}]}}, "name": "设置搜索URL", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [520, 300], "id": "set-url-1"}, {"parameters": {"requestMethod": "POST", "url": "http://127.0.0.1:8000/chrome_navigate", "jsonParameters": {"parameters": [{"name": "url", "value": "={{ $json.url }}"}]}, "options": {"headers": {"Content-Type": "application/json"}}}, "name": "打开浏览器", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [740, 300], "id": "browser-1"}, {"parameters": {"values": {"boolean": [{"name": "success", "value": true}], "string": [{"name": "message", "value": "成功打开百度搜索页面"}, {"name": "url", "value": "={{ $('设置搜索URL').item.json.url }}"}]}}, "name": "显示结果", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [960, 300], "id": "result-1"}], "connections": {"开始": {"main": [[{"node": "设置搜索URL", "type": "main", "index": 0}]]}, "设置搜索URL": {"main": [[{"node": "打开浏览器", "type": "main", "index": 0}]]}, "打开浏览器": {"main": [[{"node": "显示结果", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}