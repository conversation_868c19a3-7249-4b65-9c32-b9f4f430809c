{"name": "修复版浏览器工作流", "nodes": [{"parameters": {}, "name": "手动触发", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "id": "manual-trigger-1"}, {"parameters": {"values": {"string": [{"name": "user_input", "value": "请打开百度搜索辽宁天气预报"}]}}, "name": "设置用户输入", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300], "id": "set-input-1"}, {"parameters": {"requestMethod": "POST", "url": "http://127.0.0.1:11434/api/generate", "jsonParameters": {"parameters": [{"name": "model", "value": "qwen2.5:7b"}, {"name": "prompt", "value": "用户指令：{{ $json.user_input }}\n\n请分析这个指令，如果是搜索请求，请返回搜索URL。只返回URL，不要其他内容。例如：https://www.baidu.com/s?wd=天气预报"}, {"name": "stream", "value": false}]}, "options": {"headers": {"Content-Type": "application/json"}}}, "name": "AI分析指令", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [680, 300], "id": "ai-analysis-1"}, {"parameters": {"functionCode": "const response = items[0].json.response;\nlet url = \"https://www.baidu.com/s?wd=辽宁天气预报\";\n\nif (response && typeof response === 'string') {\n  const urlMatch = response.match(/https?:\\/\\/[^\\s]+/);\n  if (urlMatch) {\n    url = urlMatch[0];\n  }\n}\n\nreturn items.map(item => ({\n  json: {\n    url: url,\n    original_response: response\n  }\n}));"}, "name": "处理AI响应", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 300], "id": "process-ai-1"}, {"parameters": {"requestMethod": "POST", "url": "http://127.0.0.1:8000/chrome_navigate", "jsonParameters": {"parameters": [{"name": "url", "value": "={{ $json.url }}"}]}, "options": {"headers": {"Content-Type": "application/json"}}}, "name": "执行浏览器操作", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [1120, 300], "id": "browser-action-1"}, {"parameters": {"values": {"boolean": [{"name": "success", "value": true}], "string": [{"name": "message", "value": "浏览器操作执行完成"}, {"name": "executed_url", "value": "={{ $('处理AI响应').item.json.url }}"}]}}, "name": "返回结果", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1340, 300], "id": "return-result-1"}], "connections": {"手动触发": {"main": [[{"node": "设置用户输入", "type": "main", "index": 0}]]}, "设置用户输入": {"main": [[{"node": "AI分析指令", "type": "main", "index": 0}]]}, "AI分析指令": {"main": [[{"node": "处理AI响应", "type": "main", "index": 0}]]}, "处理AI响应": {"main": [[{"node": "执行浏览器操作", "type": "main", "index": 0}]]}, "执行浏览器操作": {"main": [[{"node": "返回结果", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}